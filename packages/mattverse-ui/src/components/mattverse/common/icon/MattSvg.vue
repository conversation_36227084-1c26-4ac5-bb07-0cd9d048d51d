<template>
  <img
    :src="currentSrc"
    :class="cn('w-6 h-6 object-contain', className)"
    @error="handleImageError"
  />
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { cn } from '../../../../lib/utils'

interface Props {
  name: string
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  className: '',
})

// 当前尝试的图片源
const currentSrc = ref('')
// 尝试路径的索引
const attemptIndex = ref(0)

// 生成可能的路径列表
const possiblePaths = computed(() => {
  if (!props.name.startsWith('./')) {
    // 对于字符串名称，生成多个可能的路径
    const fileName = `${props.name}.svg`
    return [
      `/svg/${fileName}`, // 根目录
      `/svg/materialDesign/${fileName}`, // 材料设计
      `/svg/batterySimulation/${fileName}`, // 电池模拟
      `/svg/processOptimization/${fileName}`, // 工艺优化
      `/svg/iconfont/${fileName}`, // 语言图标
      `/svg/AI/${fileName}`, // AI 相关
      `/svg/status/${fileName}`, // 状态图标
    ]
  }

  // 对于相对路径，移除哈希后缀
  const fileName = props.name.replace('./', '').replace(/-[A-Z0-9]{8}\.svg$/, '.svg')
  return [`/svg/${fileName}`]
})

// 处理图片加载错误，尝试下一个路径
const handleImageError = () => {
  attemptIndex.value++
  if (attemptIndex.value < possiblePaths.value.length) {
    currentSrc.value = possiblePaths.value[attemptIndex.value]
  }
}

// 监听 name 变化，重置尝试
watch(() => props.name, () => {
  attemptIndex.value = 0
  currentSrc.value = possiblePaths.value[0] || ''
}, { immediate: true })
</script>
