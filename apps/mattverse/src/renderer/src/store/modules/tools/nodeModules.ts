import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { nodeModules, type NodeModule } from '@mattverse/shared'
import { createPersistConfig } from '@/store/plugins/persist-config'

export const useNodeModulesStore = defineStore(
  'nodeModules',
  () => {
    // 存储所有节点模块，直接使用默认数据初始化
    const nodeModulesData = ref<Record<string, NodeModule>>({ ...nodeModules })

    // 初始化数据
    const initializeModules = () => {
      // 如果当前数据为空，重新加载默认数据
      if (Object.keys(nodeModulesData.value).length === 0) {
        nodeModulesData.value = { ...nodeModules }
      }
    }

    // 获取所有模块
    const getAllModules = computed(() => {
      return nodeModulesData.value
    })

    // 获取启用的模块
    const getEnabledModules = computed(() => {
      return Object.fromEntries(
        Object.entries(nodeModulesData.value).filter(([_, module]) => module.enabled)
      )
    })

    // 获取禁用的模块
    const getDisabledModules = computed(() => {
      return Object.fromEntries(
        Object.entries(nodeModulesData.value).filter(([_, module]) => !module.enabled)
      )
    })

    // 获取内置模块
    const getBuiltinModules = computed(() => {
      return Object.fromEntries(
        Object.entries(nodeModulesData.value).filter(([_, module]) => module.isBuiltin)
      )
    })

    // 切换模块启用状态
    const toggleModuleEnabled = (moduleName: string) => {
      if (nodeModulesData.value[moduleName]) {
        nodeModulesData.value[moduleName].enabled = !nodeModulesData.value[moduleName].enabled
      }
    }

    // 设置模块启用状态
    const setModuleEnabled = (moduleName: string, enabled: boolean) => {
      if (nodeModulesData.value[moduleName]) {
        nodeModulesData.value[moduleName].enabled = enabled
      }
    }

    // 获取单个模块
    const getModule = (moduleName: string) => {
      return nodeModulesData.value[moduleName] || null
    }

    // 检查模块是否启用
    const isModuleEnabled = (moduleName: string) => {
      return nodeModulesData.value[moduleName]?.enabled || false
    }

    // 获取模块列表（用于展示）
    const getModuleList = computed(() => {
      const result = Object.entries(nodeModulesData.value).map(([key, module]) => ({
        key,
        name: module.name,
        description: module.description,
        icon: module.icon,
        enabled: module.enabled,
        isBuiltin: module.isBuiltin,
        type: module.type,
        categoriesCount: module.categories?.length || 0,
        nodesCount:
          module.categories?.reduce(
            (total, category) => total + (category.nodes?.length || 0),
            0
          ) || 0,
      }))

      // 临时调试信息
      if (result.length === 0) {
        console.warn('模块列表为空，原始数据:', nodeModulesData.value)
        console.warn('默认模块数据:', nodeModules)
      }

      return result
    })

    return {
      // 状态
      nodeModulesData,

      // 计算属性
      getAllModules,
      getEnabledModules,
      getDisabledModules,
      getBuiltinModules,
      getModuleList,

      // 方法
      initializeModules,
      toggleModuleEnabled,
      setModuleEnabled,
      getModule,
      isModuleEnabled,
    }
  },
  {
    persist: createPersistConfig('nodeModules'),
  }
)
