import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { nodeModules, type NodeModule } from '@mattverse/shared'
import { createPersistConfig } from '@/store/plugins/persist-config'

export const useNodeModulesStore = defineStore(
  'nodeModules',
  () => {
    // 存储所有节点模块，初始化时合并默认模块和持久化数据
    const nodeModulesData = ref<Record<string, NodeModule>>({})

    // 初始化数据
    const initializeModules = () => {
      console.log('初始化模块数据，当前数据长度:', Object.keys(nodeModulesData.value).length)
      console.log('默认模块数据:', nodeModules)

      // 如果没有持久化数据，使用默认数据
      if (Object.keys(nodeModulesData.value).length === 0) {
        console.log('使用默认数据初始化')
        nodeModulesData.value = { ...nodeModules }
      } else {
        console.log('合并持久化数据和默认数据')
        // 合并默认数据和持久化数据，确保新增的模块能被加载
        const mergedData = { ...nodeModules }
        Object.keys(nodeModulesData.value).forEach(key => {
          if (mergedData[key]) {
            // 保留持久化的 enabled 状态
            mergedData[key] = {
              ...mergedData[key],
              enabled: nodeModulesData.value[key].enabled,
            }
          }
        })
        nodeModulesData.value = mergedData
      }

      console.log('初始化完成，最终数据:', nodeModulesData.value)
      console.log('模块数量:', Object.keys(nodeModulesData.value).length)
    }

    // 获取所有模块
    const getAllModules = computed(() => {
      return nodeModulesData.value
    })

    // 获取启用的模块
    const getEnabledModules = computed(() => {
      return Object.fromEntries(
        Object.entries(nodeModulesData.value).filter(([_, module]) => module.enabled)
      )
    })

    // 获取禁用的模块
    const getDisabledModules = computed(() => {
      return Object.fromEntries(
        Object.entries(nodeModulesData.value).filter(([_, module]) => !module.enabled)
      )
    })

    // 获取内置模块
    const getBuiltinModules = computed(() => {
      return Object.fromEntries(
        Object.entries(nodeModulesData.value).filter(([_, module]) => module.isBuiltin)
      )
    })

    // 切换模块启用状态
    const toggleModuleEnabled = (moduleName: string) => {
      if (nodeModulesData.value[moduleName]) {
        nodeModulesData.value[moduleName].enabled = !nodeModulesData.value[moduleName].enabled
      }
    }

    // 设置模块启用状态
    const setModuleEnabled = (moduleName: string, enabled: boolean) => {
      if (nodeModulesData.value[moduleName]) {
        nodeModulesData.value[moduleName].enabled = enabled
      }
    }

    // 获取单个模块
    const getModule = (moduleName: string) => {
      return nodeModulesData.value[moduleName] || null
    }

    // 检查模块是否启用
    const isModuleEnabled = (moduleName: string) => {
      return nodeModulesData.value[moduleName]?.enabled || false
    }

    // 获取模块列表（用于展示）
    const getModuleList = computed(() => {
      return Object.entries(nodeModulesData.value).map(([key, module]) => ({
        key,
        name: module.name,
        description: module.description,
        icon: module.icon,
        enabled: module.enabled,
        isBuiltin: module.isBuiltin,
        type: module.type,
        categoriesCount: module.categories?.length || 0,
        nodesCount:
          module.categories?.reduce(
            (total, category) => total + (category.nodes?.length || 0),
            0
          ) || 0,
      }))
    })

    // 初始化
    initializeModules()

    return {
      // 状态
      nodeModulesData,

      // 计算属性
      getAllModules,
      getEnabledModules,
      getDisabledModules,
      getBuiltinModules,
      getModuleList,

      // 方法
      initializeModules,
      toggleModuleEnabled,
      setModuleEnabled,
      getModule,
      isModuleEnabled,
    }
  },
  {
    persist: createPersistConfig('nodeModules'),
  }
)
